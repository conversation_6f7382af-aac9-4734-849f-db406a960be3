<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M32.7627 10.083H39.417V36.6663H33.238H32.7627H26.5837V14.2804H32.7627V10.083Z" fill="url(#paint0_linear)"/>
<rect opacity="0.8" x="6.41699" y="6.4165" width="7.33333" height="30.25" fill="url(#paint1_linear)"/>
<rect opacity="0.8" x="4.5835" y="19.25" width="5.5" height="12.8333" fill="url(#paint2_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.917 21.1989C33.917 20.4682 33.5185 19.7957 32.8776 19.4447L27.5003 16.5V37.5833H31.917C33.0216 37.5833 33.917 36.6879 33.917 35.5833V21.1989Z" fill="url(#paint3_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 12.0939L28.4167 9.1665V37.5832H10.25C9.14543 37.5832 8.25 36.6877 8.25 35.5832V12.0939Z" fill="url(#paint4_linear)"/>
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="8" y="9" width="21" height="29">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 12.0939L28.4167 9.1665V37.5832H10.25C9.14543 37.5832 8.25 36.6877 8.25 35.5832V12.0939Z" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect width="7.33333" height="10.0833" rx="1" transform="matrix(-1 0 0 1 22 30.25)" fill="url(#paint5_linear)"/>
</g>
<rect width="9.16667" height="1.83333" rx="0.916666" transform="matrix(-1 0 0 1 22.917 16.5)" fill="white"/>
<rect width="9.16667" height="1.83333" rx="0.916667" transform="matrix(-1 0 0 1 22.917 20.625)" fill="white"/>
<rect width="9.16667" height="1.83333" rx="0.916667" transform="matrix(-1 0 0 1 22.917 24.75)" fill="white"/>
<rect x="34.8335" y="32.083" width="1.83333" height="5.5" rx="0.916664" fill="url(#paint6_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.7502 33C37.7752 33 39.4168 31.1532 39.4168 28.875C39.4168 26.5968 37.7752 24.75 35.7502 24.75C33.7251 24.75 32.0835 26.5968 32.0835 28.875C32.0835 31.1532 33.7251 33 35.7502 33Z" fill="url(#paint7_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.3554 9.22693C29.2311 8.67704 28.7019 8.31756 28.1449 8.40455L8.46623 11.4778C7.85335 11.5736 7.4495 12.168 7.58636 12.7731V12.7731C7.71074 13.323 8.23992 13.6824 8.79695 13.5954L28.4756 10.5222C29.0884 10.4264 29.4923 9.83195 29.3554 9.22693V9.22693Z" fill="url(#paint8_linear)"/>
<defs>
<linearGradient id="paint0_linear" x1="29.7091" y1="36.6663" x2="29.7091" y2="15.2553" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.239811"/>
<stop offset="1" stop-color="#FFDF8F"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="11.9644" y1="36.6665" x2="11.9644" y2="12.3023" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.239811"/>
<stop offset="1" stop-color="#FFDF8F"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="8.74403" y1="32.0833" x2="8.74403" y2="21.747" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.239811"/>
<stop offset="1" stop-color="#FFDF8F"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="26.2712" y1="12.8827" x2="33.917" y2="12.8827" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9D26"/>
<stop offset="1" stop-color="#FFB742"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="4.14738" y1="40.4737" x2="28.4167" y2="40.4737" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC65C"/>
<stop offset="1" stop-color="#FFC65C"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="5.5" y1="2.52083" x2="0.703603" y2="-0.967454" gradientUnits="userSpaceOnUse">
<stop stop-color="#E66F0C"/>
<stop offset="1" stop-color="#EF8011"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="34.8335" y1="32.083" x2="34.8335" y2="37.583" gradientUnits="userSpaceOnUse">
<stop stop-color="#EE7D10"/>
<stop offset="1" stop-color="#E66F0C"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="35.3608" y1="25.2925" x2="31.1675" y2="30.4654" gradientUnits="userSpaceOnUse">
<stop stop-color="#E6700C"/>
<stop offset="1" stop-color="#E66F0C"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="24.2147" y1="9.31527" x2="24.2147" y2="12.0273" gradientUnits="userSpaceOnUse">
<stop stop-color="#E67A38"/>
<stop offset="1" stop-color="#E66F0C"/>
</linearGradient>
</defs>
</svg>
