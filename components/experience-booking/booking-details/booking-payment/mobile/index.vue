<template>
  <div class="common-mweb-card booking-payment-box">
    <h2 class="common-group-name common-row-flex">
      <span class="common-group-name__title">
        {{ businessData.group_name }}
      </span>
    </h2>
    <div class="booking-body-box">
      <TextList
        style="padding: 12px 0 6px 0;"
        :data="contendArr"
        :reset-class="'style-txt'"
        :item-style="{ margin: '0 0 6px 0' }"
      />
      <div v-if="tipsArr && tipsArr.length" class="bg-tips-box">
        <p v-for="(item, i) in tipsArr" :key="i" class="bg-tips-box__text">
          {{ item.text }}
          <span
            v-if="item.action && item.action.text"
            class="bg-tips-box__action"
            @click="clickBtn(item.action)"
          >
            <klk-icon size="16" type="icon_tips_tips"></klk-icon>
          </span>
        </p>
      </div>
      <TextTips
        v-for="(btn, idx) in actionArr"
        :key="idx"
        v-bind="{
          ...btn,
          size: 'mini'
        }"
        :reset-class="'style-btn'"
        @click="clickBtn(btn)"
      />
    </div>
    <div class="track-node"></div>
    <!-- 印花税弹窗 -->
    <klk-modal
      title=""
      button-align="equal"
      :open.sync="openModel"
      :show-cancel-button="false"
    >
      <div class="text-align-center__frf">{{ modelContent }}</div>
      <div slot="footer">
        <klk-button
          size="small"
          style="width: 100%; margin-top: 20px"
          @click="closeModel"
        >
          {{ $t('global.confirm') }}
        </klk-button>
      </div>
    </klk-modal>
    <!-- 埋点 FrankedReceiptFailed -->
    <div
      v-galileo-click-tracker="{
        spm: '',
        autoTrackSpm: true
      }"
      class="data-spm-module_FrankedReceiptFailed"

      :data-spm-module="'FrankedReceiptFailed' + spm"
      :data-spm-virtual-item="'__virtual' + spm"
    ></div>
  </div>
</template>

<script lang="ts">
import { Component } from 'nuxt-property-decorator'
import { BusinessBase } from '~/components/setion/experience/base/business-base'
import { ExperienceBookingDetails } from '~/types/experience/booking-details'
import { emit as myEmit, on as myOn } from '~/components/experience-booking/booking-details/base/common'
import { openDeeplink } from '~/components/experience-booking/booking-details/utils'

@Component({
  components: {}
})
export default class index extends BusinessBase {
  static displayName = 'MobileExperienceBooking_info'
  get contendArr() {
    const arr = this.businessData.content || []
    return arr
  }

  get tipsArr() {
    return this.businessData.tips || []
  }

  get actionArr() {
    return this.businessData.action || []
  }

  get detailBasic() {
    const obj: any = this.$attrs['detail-basic'] || {}
    return obj
  }

  beforeMount() {
    // 订阅印花税按钮事件
    myOn('issu_franked_receipt', (btn: ExperienceBookingDetails.ActionIF) => {
      btn && this.clickBtn(btn)
    })
  }

  openModel: boolean = false
  modelContent: string = ''

  closeModel() {
    this.openModel = false
    this.$inhouse.track('action', '.data-spm-module_FrankedReceiptFailed')
  }

  get spm() {
    const oid = '?trg=manual&oid=booking_' + this.detailBasic.booking_no
    return oid
  }

  openDeeplink(href: string) {
    openDeeplink(href, this.klook.platform)
  }

  clickBtn(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      case 'order_info_view_more': // 合并支付、查看更多弹窗
        myEmit('handleMergeOption', this.detailBasic.booking_id, '')
        break
      // 印花税收据
      // case 'issue_franked_receipt': // 开具收据
      // case 'view_franked_receipt_success': // 开具成功
      case 'issuing_franked_receipt': // 开办中 text id 13496
        this.$alert(this.$t('13496'))
        break
      case 'view_franked_receipt_fail': // 开具失败 text id 13782
        // 弹窗
        this.openModel = true
        this.modelContent = this.$t('13782')
        this.$inhouse.track('exposure', '.data-spm-module_FrankedReceiptFailed')
        break
      default:
        // deep_link
        btn.deep_link && this.openDeeplink(btn.deep_link)
    }
  }
}
</script>

<style lang="scss" scoped>

.text-align-center__frf {
  text-align: center;
  line-height: 22px;
}

.booking-payment-box {
  .booking-body-box {
    padding: 0 0 16px 0;
  }

  .bg-tips-box {
    margin: 0 0 16px 0;
    padding: 8px 16px;
    background: $color-bg-page;
    border-radius: $radius-s;

    &__text {
      font-size: $fontSize-caption-m;
      line-height: 16px;
      color: $color-text-secondary;

      &:not(:last-of-type) {
        margin-bottom: 4px;
      }
    }

    &__action {
      cursor: pointer;
      text-decoration: underline;
    }
  }
}
</style>
