<template>
  <div class="common-booking-details-desktop booking-payment-box">
    <h2 class="common-group-name common-row-flex">
      <span class="common-group-name__title">{{ businessData.group_name }}</span>
    </h2>
    <klk-divider class="common-line"></klk-divider>
    <div class="booking-body-box">
      <TextList style="padding-bottom: 4px;" :data="contendArr" :reset-class="'style-txt'" :item-style="{width: '50%', display: 'inline-block', margin: '0 0 12px 0'}" />
      <div v-if="tipsArr && tipsArr.length" class="bg-tips-box">
        <p v-for="(item, i) in tipsArr" :key="i" class="bg-tips-box__text">
          {{ item.text }}
          <span v-if="item.action && item.action.text" class="bg-tips-box__action" @click="clickBtn(item.action)">{{ item.action.text }}</span>
        </p>
      </div>
      <TextTips
        v-for="(btn, idx) in actionArr"
        :key="idx"
        v-bind="{
          ...btn,
          size: 'small',
        }"
        :reset-class="'style-btn'"
        @click="clickBtn(btn)"
      />
    </div>

    <!-- 印花税弹窗 -->
    <klk-modal
      title=""
      button-align="equal"
      :open.sync="openModel"
      :show-cancel-button="false"
    >
      <span>{{ modelContent }}</span>
      <div slot="footer" class="klk-modal-footer-btn-wrap">
        <klk-button
          size="small"
          @click="closeModel"
        >
          {{ $t('global.confirm') }}
        </klk-button>
      </div>
    </klk-modal>
    <!-- 填写开具资料 -->
    <klk-modal
      v-bind="{...issueReceipt, title: $t('14061')}"
      @on-close="closeIssueReceipt"
    >
      <IssueFrankedReceipt :order-no="detailBasic.booking_id" @submit="submitReceipt" />
    </klk-modal>

    <!-- 埋点 FrankedReceiptFailed -->
    <div
      v-galileo-click-tracker="{
        spm: '',
        autoTrackSpm: true
      }"
      class="data-spm-module_FrankedReceiptFailed"

      :data-spm-module="'FrankedReceiptFailed' + spm"
      :data-spm-virtual-item="'__virtual' + spm"
    ></div>
  </div>
</template>

<script lang="ts">
import { Component } from 'nuxt-property-decorator'
import { BusinessBase } from '~/components/setion/experience/base/business-base'
import { ExperienceBookingDetails } from '~/types/experience/booking-details'
import { emit as myEmit, on as myOn } from '~/components/experience-booking/booking-details/base/common'
import { openDeeplink, getModalOpt } from '~/components/experience-booking/booking-details/utils'
import IssueFrankedReceipt from '~/components/experience-booking/booking-details/components/issue-franked-receipt/index.vue'

@Component({
  components: {
    IssueFrankedReceipt
  }
})
export default class index extends BusinessBase {
  static displayName = 'DesktopExperienceBooking_info'
  get contendArr() {
    const arr = this.businessData.content || []
    return arr
  }

  get tipsArr() {
    return this.businessData.tips || []
  }

  get actionArr() {
    return this.businessData.action || []
  }

  get detailBasic() {
    const obj: any = this.$attrs['detail-basic'] || {}
    return obj
  }

  openModel: boolean = false
  modelContent: string = ''

  closeModel() {
    this.openModel = false
    this.$inhouse.track('action', '.data-spm-module_FrankedReceiptFailed')
  }

  get spm() {
    const oid = '?trg=manual&oid=booking_' + this.detailBasic.booking_no
    return oid
  }

  // 开具印花税弹窗
  issueReceipt = {
    ...getModalOpt({
      width: '960px',
      titleAlign: 'center',
      size: 'normal',
      escPressClosable: false,
      overlayClosable: false
    })
  }

  beforeMount() {
    // 订阅印花税按钮事件
    myOn('issu_franked_receipt', (btn: ExperienceBookingDetails.ActionIF) => {
      btn && this.clickBtn(btn)
    })
  }

  submitReceipt(success: boolean) {
    // 成功之后改变按钮状态
    if (success) {
      this.businessData.action = this.businessData.action.map((btn: ExperienceBookingDetails.ActionIF) => {
        if (btn.key === 'issue_franked_receipt') {
          btn.key = 'issuing_franked_receipt'
          btn.text = this.$t('13844')
        }
        return btn
      })
    }
    this.issueReceipt.open = false
  }

  async closeIssueReceipt() {
    const okLabel = this.$t('global.confirm')
    const confirm = await this.$confirm(this.$t('14774'), this.$t('15971'), {
      okLabel
    })
    if (confirm.result) {
      this.issueReceipt.open = false
    }
  }

  openDeeplink(href: string) {
    openDeeplink(href, this.klook.platform)
  }

  clickBtn(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      case 'order_info_view_more': // 合并支付、查看更多弹窗
        myEmit('handleMergeOption', this.detailBasic.booking_id, '')
        break
      case 'issue_franked_receipt': // 开具收据
        this.issueReceipt.open = true
        break
      case 'issuing_franked_receipt': // 印花税开办中 text id 13496
        this.$alert(this.$t('13496'))
        break
      case 'view_franked_receipt_fail': // 开具失败 text id 13782
        // 弹窗
        this.openModel = true
        this.modelContent = this.$t('13782')
        this.$inhouse.track('exposure', '.data-spm-module_FrankedReceiptFailed')
        break
      default:
        // deep_link
        btn.deep_link && this.openDeeplink(btn.deep_link)
    }
  }
}
</script>

<style lang="scss" scoped>

.footer-btn-align-right {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.klk-modal-footer-btn-wrap {
  padding-top: 20px;
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.booking-payment-box {
  padding: 32px 32px 32px 32px;

  .bg-tips-box {
    margin: 0 0 16px 0;
    padding: 8px 16px;
    background: $color-bg-page;
    border-radius: $radius-s;

    &__text {
      font-size: $fontSize-caption-m;
      line-height: 16px;
      color: $color-text-secondary;

      &:not(:last-of-type) {
        margin-bottom: 4px;
      }
    }

    &__action {
      cursor: pointer;
      text-decoration: underline;
    }
  }
}
</style>
