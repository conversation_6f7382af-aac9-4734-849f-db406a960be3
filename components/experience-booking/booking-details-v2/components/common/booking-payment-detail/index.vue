<template>
  <KlookPaymentDetail
    :language="klook.language"
    :platform="klook.platform"
    :visible.sync="data.visible"
    :booking-ref-no="data.booking_ref_no"
    :payment-details="getPaymentDetails"
    @custom-button-click="click"
    v-on="$listeners"
  >
    <template v-slot:price-detail>
      <KlookOrderTableAtomic :data="orderDetailData"></KlookOrderTableAtomic>
    </template>
  </KlookPaymentDetail>
</template>

<script lang="ts">
import { Vue, Component, State, Prop } from 'nuxt-property-decorator'
import KlookPaymentDetail from '@klook/klook-payment-detail'
import '@klook/klook-payment-detail/dist/esm/index.css'
import KlookOrderTableAtomic from '@klook/klook-order-table-atomic'
import '@klook/klook-order-table-atomic/dist/esm/index.css'

@Component({
  components: {
    KlookOrderTableAtomic,
    KlookPaymentDetail
  }
})
export default class KlookPaymentWarp extends Vue {
  @State klook!: Data.Klook
  @Prop({ type: Object, default: null }) data!: any

  get getPaymentDetails() {
    return this.data?.payment_details || {}
  }

  get orderDetailData() {
    return this.data?.booking_items || []
  }

  click(name: string, index: number) {
    console.log(name)
  }
}
</script>
