<template>
  <div>
    <KlookPaymentDetail
      :language="klook.language"
      :platform="klook.platform"
      :visible.sync="data.visible"
      :booking-ref-no="data.booking_ref_no"
      :payment-details="getPaymentDetails"
      @custom-button-click="click"
      v-on="$listeners"
    >
      <template v-slot:price-detail>
        <KlookOrderTableAtomic :data="orderDetailData"></KlookOrderTableAtomic>
      </template>
    </KlookPaymentDetail>

    <!-- 印花税失败弹窗 -->
    <klk-modal
      title=""
      button-align="equal"
      :open.sync="openModel"
      :show-cancel-button="false"
    >
      <span>{{ modelContent }}</span>
      <div slot="footer" class="klk-modal-footer-btn-wrap">
        <klk-button
          size="small"
          @click="closeModel"
        >
          {{ $t('global.confirm') }}
        </klk-button>
      </div>
    </klk-modal>

    <!-- 填写开具资料弹窗 -->
    <klk-modal
      v-bind="{...issueReceipt, title: $t('14061')}"
      @on-close="closeIssueReceipt"
    >
      <IssueFrankedReceipt :order-no="data.booking_ref_no" @submit="submitReceipt" />
    </klk-modal>

    <!-- 埋点 FrankedReceiptFailed -->
    <div
      v-galileo-click-tracker="{
        spm: '',
        autoTrackSpm: true
      }"
      class="data-spm-module_FrankedReceiptFailed"
      :data-spm-module="'FrankedReceiptFailed' + spm"
      :data-spm-virtual-item="'__virtual' + spm"
    ></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, State, Prop } from 'nuxt-property-decorator'
import KlookPaymentDetail from '@klook/klook-payment-detail'
import '@klook/klook-payment-detail/dist/esm/index.css'
import KlookOrderTableAtomic from '@klook/klook-order-table-atomic'
import '@klook/klook-order-table-atomic/dist/esm/index.css'
import { ExperienceBookingDetails } from '~/types/experience/booking-details'
import { emit as myEmit, on as myOn } from '~/components/experience-booking/booking-details/base/common'
import { openDeeplink, getModalOpt } from '~/components/experience-booking/booking-details/utils'
import IssueFrankedReceipt from '~/components/experience-booking/booking-details/components/issue-franked-receipt/index.vue'

@Component({
  components: {
    KlookOrderTableAtomic,
    KlookPaymentDetail,
    IssueFrankedReceipt
  }
})
export default class KlookPaymentWarp extends Vue {
  @State klook!: Data.Klook
  @Prop({ type: Object, default: null }) data!: any

  // 印花税相关状态
  openModel: boolean = false
  modelContent: string = ''

  // 开具印花税弹窗配置
  issueReceipt = {
    ...getModalOpt({
      width: '960px',
      titleAlign: 'center',
      size: 'normal',
      escPressClosable: false,
      overlayClosable: false
    })
  }

  get getPaymentDetails() {
    return this.data?.payment_details || {}
  }

  get orderDetailData() {
    return this.data?.booking_items || []
  }

  get isDesktop() {
    return this.klook.platform === 'desktop'
  }

  get spm() {
    const oid = '?trg=manual&oid=booking_' + this.data?.booking_ref_no
    return oid
  }

  beforeMount() {
    // 订阅印花税按钮事件
    myOn('issu_franked_receipt', (btn: ExperienceBookingDetails.ActionIF) => {
      btn && this.handleFrankedReceiptAction(btn)
    })
  }

  click(_name: string, index: number) {
    // 获取按钮数据
    const actionList = this.getPaymentDetails?.action || []
    const btn = actionList[index]

    if (btn) {
      this.handleFrankedReceiptAction(btn)
    }
  }

  handleFrankedReceiptAction(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      case 'issue_franked_receipt': // 开具收据
        if (this.isDesktop) {
          this.issueReceipt.open = true
        } else {
          btn.deep_link && this.openDeeplink(btn.deep_link)
        }
        break
      case 'issuing_franked_receipt': // 印花税开办中 text id 13496
        this.$alert(this.$t('13496'))
        break
      case 'view_franked_receipt_fail': // 开具失败 text id 13782
        // 弹窗
        this.openModel = true
        this.modelContent = this.$t('13782')
        this.$inhouse.track('exposure', '.data-spm-module_FrankedReceiptFailed')
        break
      case 'order_info_view_more': // 合并支付、查看更多弹窗
        myEmit('handleMergeOption2on', this.data?.order_guid, '')
        break
      default:
        // deep_link
        btn.deep_link && this.openDeeplink(btn.deep_link)
    }
  }

  openDeeplink(href: string) {
    openDeeplink(href, this.klook.platform)
  }

  async closeIssueReceipt() {
    const okLabel = this.$t('global.confirm')
    const confirm = await this.$confirm(this.$t('14774'), this.$t('15971'), {
      okLabel
    })
    if (confirm.result) {
      this.issueReceipt.open = false
    }
  }

  submitReceipt(success: boolean) {
    // 成功之后改变按钮状态
    if (success) {
      const actionList = this.getPaymentDetails?.action || []
      actionList.forEach((btn: ExperienceBookingDetails.ActionIF) => {
        if (btn.key === 'issue_franked_receipt') {
          btn.key = 'issuing_franked_receipt'
          btn.text = this.$t('13844')
        }
      })
    }
    this.issueReceipt.open = false
  }

  closeModel() {
    this.openModel = false
    this.$inhouse.track('action', '.data-spm-module_FrankedReceiptFailed')
  }
}
</script>
